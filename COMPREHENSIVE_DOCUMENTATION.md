# 🏗️ New Builder - Comprehensive Documentation

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Microservices Breakdown](#microservices-breakdown)
4. [Technology Stack](#technology-stack)
5. [Development Setup](#development-setup)
6. [Deployment Strategy](#deployment-strategy)
7. [API Documentation](#api-documentation)
8. [Database Schema](#database-schema)
9. [Authentication & Authorization](#authentication--authorization)
10. [Testing Strategy](#testing-strategy)
11. [Migration Status](#migration-status)
12. [Contributing Guidelines](#contributing-guidelines)

---

## 🎯 Project Overview

**New Builder** is a comprehensive microservices-based website builder platform that enables users to create, customize, and deploy websites through a drag-and-drop interface. This project represents a complete architectural migration from a legacy monolithic system to a modern, scalable microservices architecture.

### Key Features
- **Drag & Drop Builder**: Intuitive visual website editor
- **Template System**: Pre-built templates with customization options
- **Multi-tenant Architecture**: Support for multiple users and organizations
- **Real-time Collaboration**: Live editing and preview capabilities
- **Domain Management**: Custom domain registration and DNS management
- **Analytics Integration**: Built-in website analytics and tracking
- **Billing & Subscription**: Stripe-powered payment processing
- **Multi-language Support**: RTL and Arabic language support
- **Role-based Access Control**: Admin, user, and support roles

---

## 🏛️ Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        BE[Builder Editor]
        AD[Admin Dashboard]
        SD[Site Dashboard]
        BD[Backoffice Dashboard]
    end
    
    subgraph "API Gateway Layer"
        AG[API Gateway/Load Balancer]
    end
    
    subgraph "Microservices Layer"
        AS[Auth Service]
        BA[Builder API]
        TS[Templates Service]
        MS[Media Service]
        PS[Publish Service]
        DS[Domain Service]
        BS[Billing Service]
        ANS[Analytics Service]
        QS[Questionnaire Service]
        IS[Invitation Service]
        GS[Geo Service]
        CI[CRM Integration]
        BO[Backoffice API]
        TEST[Test Service]
    end
    
    subgraph "Data Layer"
        SB[(Supabase)]
        ST[Supabase Storage]
        STRIPE[Stripe API]
    end
    
    subgraph "Infrastructure"
        VERCEL[Vercel Deployment]
        DNS[DNS Management]
    end
    
    BE --> AG
    AD --> AG
    SD --> AG
    BD --> AG
    
    AG --> AS
    AG --> BA
    AG --> TS
    AG --> MS
    AG --> PS
    AG --> DS
    AG --> BS
    AG --> ANS
    AG --> QS
    AG --> IS
    AG --> GS
    AG --> CI
    AG --> BO
    AG --> TEST
    
    AS --> SB
    BA --> SB
    TS --> SB
    MS --> ST
    PS --> SB
    DS --> SB
    BS --> STRIPE
    ANS --> SB
    QS --> SB
    IS --> SB
    GS --> SB
    CI --> SB
    BO --> SB
    
    PS --> VERCEL
    DS --> DNS
```

### Architectural Principles
- **Microservices Pattern**: Each service handles a specific business domain
- **API-First Design**: RESTful APIs with consistent interfaces
- **Event-Driven Architecture**: Asynchronous communication between services
- **Database per Service**: Each microservice owns its data
- **Stateless Services**: Horizontal scaling capability
- **Circuit Breaker Pattern**: Fault tolerance and resilience

---

## 🔧 Microservices Breakdown

### Core Services

#### 1. **Auth Service** (`auth-service`)
**Purpose**: Centralized authentication and authorization
- **Responsibilities**:
  - User authentication via Supabase Auth
  - JWT token validation and refresh
  - Role-based access control (RBAC)
  - Session management
- **Key Files**:
  - `src/lib/authHelpers.ts` - Authentication utilities
  - `src/routes/authRoutes.ts` - Auth endpoints
  - `src/lib/supabaseClient.ts` - Supabase integration
- **API Endpoints**:
  - `GET /auth/me` - Get current user
  - `POST /auth/login` - User login
  - `POST /auth/logout` - User logout
  - `POST /auth/refresh` - Refresh token

#### 2. **Builder API** (`builder-api`)
**Purpose**: Core website building functionality
- **Responsibilities**:
  - Site creation and management
  - Page structure and content
  - Component management
  - Site settings and configuration
- **Key Features**:
  - CRUD operations for sites and pages
  - Component library management
  - Site versioning and history
  - Export/import functionality

#### 3. **Builder Editor** (`builder-editor`)
**Purpose**: Frontend interface for the visual editor
- **Responsibilities**:
  - Drag-and-drop interface
  - Real-time preview
  - Component palette
  - Property panels
- **Technology**: React + TypeScript + Next.js
- **Key Features**:
  - Visual editing interface
  - Component library
  - Responsive design tools
  - Undo/redo functionality

#### 4. **Templates Service** (`templates-service`)
**Purpose**: Template management and distribution
- **Responsibilities**:
  - Template catalog management
  - Template categorization
  - User template creation
  - Template marketplace
- **Features**:
  - Public and private templates
  - Template versioning
  - Template preview generation
  - Category and tag management

#### 5. **Media Service** (`media-service`)
**Purpose**: File upload and media management
- **Responsibilities**:
  - File upload handling
  - Image optimization
  - Media library management
  - CDN integration
- **Storage**: Supabase Storage
- **Supported Formats**: Images, videos, documents
- **Features**:
  - Automatic image optimization
  - Thumbnail generation
  - Media organization
  - Usage tracking

#### 6. **Publish Service** (`publish-service`)
**Purpose**: Site deployment and hosting
- **Responsibilities**:
  - Site compilation and build
  - Deployment to hosting platforms
  - SSL certificate management
  - Performance optimization
- **Deployment Targets**: Vercel, custom hosting
- **Features**:
  - Automated deployments
  - Build optimization
  - Cache management
  - Rollback capabilities

#### 7. **Domain Service** (`domain-service`)
**Purpose**: Domain registration and DNS management
- **Responsibilities**:
  - Domain availability checking
  - Domain registration
  - DNS record management
  - SSL certificate provisioning
- **Integrations**: Domain registrars, DNS providers
- **Features**:
  - Custom domain setup
  - Subdomain management
  - DNS propagation monitoring
  - Domain transfer support

### Business Services

#### 8. **Billing Service** (`billing-service`)
**Purpose**: Payment processing and subscription management
- **Responsibilities**:
  - Subscription management
  - Payment processing
  - Invoice generation
  - Usage tracking
- **Integration**: Stripe
- **Features**:
  - Multiple subscription tiers
  - Usage-based billing
  - Payment method management
  - Billing history

#### 9. **Analytics Service** (`analytics-service`)
**Purpose**: Website analytics and tracking
- **Responsibilities**:
  - Visitor tracking
  - Performance metrics
  - Conversion tracking
  - Custom events
- **Features**:
  - Real-time analytics
  - Custom dashboards
  - Export capabilities
  - Privacy compliance

#### 10. **Questionnaire Service** (`questionnaire-service`)
**Purpose**: Form builder and submission handling
- **Responsibilities**:
  - Dynamic form creation
  - Form submission processing
  - Data validation
  - Response management
- **Features**:
  - Drag-and-drop form builder
  - Conditional logic
  - File uploads in forms
  - Integration with CRM systems

### Administrative Services

#### 11. **Admin Dashboard** (`admin-dashboard`)
**Purpose**: System administration interface
- **Responsibilities**:
  - User management
  - System monitoring
  - Configuration management
  - Analytics overview
- **Technology**: Next.js + React
- **Access**: Admin role only

#### 12. **Backoffice API** (`backoffice-api`)
**Purpose**: Internal operations API
- **Responsibilities**:
  - Support operations
  - Data management
  - System maintenance
  - Reporting
- **Access**: Support and admin roles

#### 13. **Backoffice Dashboard** (`backoffice-dashboard`)
**Purpose**: Support team interface
- **Responsibilities**:
  - Customer support tools
  - Issue tracking
  - User impersonation
  - System diagnostics
- **Technology**: Next.js + React

#### 14. **Site Dashboard** (`site-dashboard`)
**Purpose**: User site management interface
- **Responsibilities**:
  - Site overview
  - Analytics dashboard
  - Settings management
  - Team collaboration
- **Technology**: Next.js + React

### Integration Services

#### 15. **CRM Integration** (`crm-integration`)
**Purpose**: Third-party CRM system integration
- **Responsibilities**:
  - Lead synchronization
  - Contact management
  - Sales pipeline integration
  - Marketing automation
- **Supported CRMs**: Salesforce, HubSpot, Pipedrive

#### 16. **Invitation Service** (`invitation-service`)
**Purpose**: User invitation and onboarding
- **Responsibilities**:
  - Team invitations
  - User onboarding flows
  - Permission management
  - Notification handling

#### 17. **Geo Service** (`geo-service`)
**Purpose**: Geographic and location services
- **Responsibilities**:
  - IP geolocation
  - Country/region detection
  - Timezone handling
  - Localization support

#### 18. **Test Service** (`test-service`)
**Purpose**: Testing and quality assurance
- **Responsibilities**:
  - Automated testing
  - Performance testing
  - Integration testing
  - Test data management

---

## 💻 Technology Stack

### Frontend Technologies
- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript
- **UI Library**: Chakra UI / Tailwind CSS
- **State Management**: Zustand / React Query
- **Build Tool**: Vite / Next.js built-in
- **Testing**: Vitest + React Testing Library

### Backend Technologies
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **API Style**: RESTful APIs
- **Authentication**: JWT with Supabase Auth
- **Validation**: Zod / Joi

### Database & Storage
- **Primary Database**: Supabase (PostgreSQL)
- **File Storage**: Supabase Storage
- **Caching**: Redis (planned)
- **Search**: Supabase Full-Text Search

### Infrastructure & Deployment
- **Hosting**: Vercel
- **CI/CD**: GitHub Actions
- **Monitoring**: Vercel Analytics
- **Error Tracking**: Sentry (planned)
- **DNS**: Cloudflare

### Third-party Integrations
- **Payments**: Stripe
- **Email**: SendGrid / Resend
- **Analytics**: Custom + Google Analytics
- **CDN**: Vercel Edge Network

---

## 🚀 Development Setup

### Prerequisites
- Node.js 18+ and npm/yarn
- Git
- Supabase account
- Vercel account (for deployment)

### Local Development

1. **Clone the Repository**
```bash
git clone https://github.com/meto002/new-builder.git
cd new-builder
```

2. **Environment Setup**
Each service requires its own environment configuration:

```bash
# Copy environment template for each service
cp Micro-Services/auth-service/.env.example Micro-Services/auth-service/.env
cp Micro-Services/builder-api/.env.example Micro-Services/builder-api/.env
# ... repeat for other services
```

3. **Install Dependencies**
```bash
# Install dependencies for all services
for dir in Micro-Services/*/; do
  if [ -f "$dir/package.json" ]; then
    echo "Installing dependencies for $dir"
    cd "$dir" && npm install && cd ../..
  fi
done
```

4. **Database Setup**
```bash
# Set up Supabase project
# 1. Create project at https://supabase.com
# 2. Run migrations (if available)
# 3. Set up Row Level Security policies
```

5. **Start Development Servers**
```bash
# Start individual services
cd Micro-Services/auth-service && npm run dev
cd Micro-Services/builder-api && npm run dev
# ... or use a process manager like PM2
```

### Development Workflow

1. **Service Development**
   - Each service is independently developable
   - Use TypeScript for type safety
   - Follow RESTful API conventions
   - Implement proper error handling

2. **Testing**
   - Unit tests with Vitest
   - Integration tests for API endpoints
   - E2E tests for critical user flows

3. **Code Quality**
   - ESLint for code linting
   - Prettier for code formatting
   - Husky for pre-commit hooks
   - TypeScript for type checking

---

## 🌐 Deployment Strategy

### Vercel Deployment

Each microservice is deployed as a separate Vercel project:

1. **Service Deployment**
```bash
# Deploy individual service
cd Micro-Services/auth-service
vercel --prod
```

2. **Environment Variables**
Set up environment variables in Vercel dashboard for each service:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `JWT_SECRET`
- `STRIPE_SECRET_KEY`

3. **Custom Domains**
Configure custom domains for each service:
- `auth.yourdomain.com` → Auth Service
- `api.yourdomain.com` → Builder API
- `builder.yourdomain.com` → Builder Editor
- `dashboard.yourdomain.com` → Site Dashboard

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy Services
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service, builder-api, builder-editor]
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Vercel
        run: |
          cd Micro-Services/${{ matrix.service }}
          vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
```

---

## 📚 API Documentation

### Authentication

All API requests (except public endpoints) require authentication:

```http
Authorization: Bearer <jwt_token>
```

### Common Response Format

```json
{
  "success": true,
  "data": {},
  "message": "Operation successful",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Service Endpoints

#### Auth Service (`/auth`)
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user
- `POST /auth/refresh` - Refresh token

#### Builder API (`/api`)
- `GET /api/sites` - List user sites
- `POST /api/sites` - Create new site
- `GET /api/sites/:id` - Get site details
- `PUT /api/sites/:id` - Update site
- `DELETE /api/sites/:id` - Delete site
- `GET /api/sites/:id/pages` - List site pages
- `POST /api/sites/:id/pages` - Create new page

#### Templates Service (`/templates`)
- `GET /templates` - List templates
- `GET /templates/:id` - Get template details
- `POST /templates` - Create template
- `PUT /templates/:id` - Update template

#### Media Service (`/media`)
- `POST /media/upload` - Upload file
- `GET /media` - List user media
- `DELETE /media/:id` - Delete media file

---

## 🗄️ Database Schema

### Core Tables

#### Users
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  full_name VARCHAR,
  avatar_url VARCHAR,
  role VARCHAR DEFAULT 'user',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Sites
```sql
CREATE TABLE sites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  title VARCHAR NOT NULL,
  domain VARCHAR UNIQUE,
  status VARCHAR DEFAULT 'draft',
  settings JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Pages
```sql
CREATE TABLE pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_id UUID REFERENCES sites(id),
  title VARCHAR NOT NULL,
  slug VARCHAR NOT NULL,
  content JSONB,
  meta_data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Templates
```sql
CREATE TABLE templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  title VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR,
  content JSONB,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Row Level Security (RLS)

```sql
-- Sites RLS Policy
CREATE POLICY "Users can only access their own sites"
ON sites FOR ALL
USING (auth.uid() = user_id);

-- Pages RLS Policy
CREATE POLICY "Users can only access pages of their sites"
ON pages FOR ALL
USING (
  site_id IN (
    SELECT id FROM sites WHERE user_id = auth.uid()
  )
);
```

---

## 🔐 Authentication & Authorization

### Authentication Flow

1. **User Registration/Login**
   - Handled by Supabase Auth
   - Supports email/password, OAuth providers
   - Returns JWT token with user metadata

2. **Token Validation**
   - Each service validates JWT tokens
   - User information extracted from token
   - Role-based access control applied

3. **Session Management**
   - Automatic token refresh
   - Secure token storage
   - Session timeout handling

### Authorization Levels

#### User Roles
- **user**: Regular platform users
- **admin**: System administrators
- **support**: Customer support team

#### Permission Matrix
| Resource | User | Admin | Support |
|----------|------|-------|---------|
| Own Sites | CRUD | CRUD | Read |
| All Sites | - | CRUD | Read |
| Templates | CRUD | CRUD | Read |
| Users | Read (self) | CRUD | Read |
| Billing | Read (self) | CRUD | Read |
| Analytics | Read (own) | Read (all) | Read (all) |

### Security Measures

- **JWT Token Security**: Short-lived access tokens with refresh tokens
- **HTTPS Only**: All communications encrypted
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Rate Limiting**: API rate limiting to prevent abuse
- **Input Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries and ORM usage

---

## 🧪 Testing Strategy

### Testing Pyramid

#### Unit Tests
- **Framework**: Vitest
- **Coverage**: Individual functions and components
- **Location**: `tests/` directory in each service
- **Command**: `npm run test`

#### Integration Tests
- **Framework**: Vitest + Supertest
- **Coverage**: API endpoints and service interactions
- **Database**: Test database with fixtures
- **Command**: `npm run test:integration`

#### End-to-End Tests
- **Framework**: Playwright
- **Coverage**: Critical user journeys
- **Environment**: Staging environment
- **Command**: `npm run test:e2e`

### Test Structure Example

```typescript
// auth-service/tests/auth.test.ts
import { describe, it, expect } from 'vitest'
import request from 'supertest'
import app from '../src/index'

describe('Auth Service', () => {
  describe('POST /auth/login', () => {
    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
      
      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('token')
    })
  })
})
```

### Testing Best Practices

- **Test Isolation**: Each test should be independent
- **Mock External Services**: Use mocks for third-party APIs
- **Test Data Management**: Use factories and fixtures
- **Continuous Testing**: Run tests on every commit
- **Coverage Reporting**: Maintain >80% code coverage

---

## 📈 Migration Status

### Current Phase: **Phase 3 - Logic Migration** (In Progress)

#### ✅ Completed Phases

**Phase 1: Architecture & Analysis**
- [x] Legacy monolith analysis
- [x] Service boundary identification
- [x] Architectural documentation
- [x] Technology stack selection

**Phase 2: Microservices Scaffolding**
- [x] Service folder structure creation
- [x] Basic service templates
- [x] Development environment setup
- [x] Deployment configuration

#### 🚧 Current Phase: Logic Migration

**In Progress:**
- [ ] Extract business logic from legacy monolith
- [ ] Implement core service functionality
- [ ] Database schema migration
- [ ] API endpoint implementation
- [ ] Authentication integration

**Priority Services:**
1. Auth Service (90% complete)
2. Builder API (30% complete)
3. Templates Service (20% complete)
4. Media Service (10% complete)

#### 📋 Upcoming Phases

**Phase 4: Supabase Configuration**
- [ ] Database schema finalization
- [ ] Row Level Security policies
- [ ] Storage bucket configuration
- [ ] Environment variable setup

**Phase 5: Deployment**
- [ ] Vercel project configuration
- [ ] Domain setup and SSL
- [ ] Environment variable deployment
- [ ] Production deployment

**Phase 6: Documentation & Testing**
- [ ] API documentation completion
- [ ] Test suite implementation
- [ ] Performance testing
- [ ] Security audit

### Migration Timeline

| Phase | Duration | Status |
|-------|----------|--------|
| Phase 1 | 2 weeks | ✅ Complete |
| Phase 2 | 3 weeks | ✅ Complete |
| Phase 3 | 6 weeks | 🚧 In Progress (Week 2) |
| Phase 4 | 2 weeks | ⏳ Pending |
| Phase 5 | 3 weeks | ⏳ Pending |
| Phase 6 | 2 weeks | ⏳ Pending |

**Total Estimated Duration**: 18 weeks
**Current Progress**: ~35% complete

---

## 🤝 Contributing Guidelines

### Development Workflow

1. **Fork and Clone**
```bash
git clone https://github.com/your-username/new-builder.git
cd new-builder
```

2. **Create Feature Branch**
```bash
git checkout -b feature/your-feature-name
```

3. **Make Changes**
- Follow coding standards
- Write tests for new functionality
- Update documentation

4. **Test Changes**
```bash
npm run test
npm run lint
npm run type-check
```

5. **Commit and Push**
```bash
git commit -m "feat: add new feature"
git push origin feature/your-feature-name
```

6. **Create Pull Request**
- Provide clear description
- Link related issues
- Request appropriate reviewers

### Coding Standards

#### TypeScript Guidelines
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Avoid `any` type usage
- Use proper error handling

#### API Design
- Follow RESTful conventions
- Use consistent response formats
- Implement proper HTTP status codes
- Include comprehensive error messages

#### Database Guidelines
- Use descriptive table and column names
- Implement proper indexing
- Follow normalization principles
- Use Row Level Security (RLS)

#### Testing Requirements
- Write unit tests for all functions
- Include integration tests for APIs
- Maintain >80% code coverage
- Use descriptive test names

### Code Review Process

1. **Automated Checks**
   - TypeScript compilation
   - Linting and formatting
   - Unit test execution
   - Security scanning

2. **Manual Review**
   - Code quality assessment
   - Architecture compliance
   - Performance considerations
   - Security review

3. **Approval Requirements**
   - At least 2 approvals for core changes
   - 1 approval for minor changes
   - All checks must pass

### Issue Management

#### Bug Reports
- Use bug report template
- Include reproduction steps
- Provide environment details
- Add relevant labels

#### Feature Requests
- Use feature request template
- Describe use case and benefits
- Include acceptance criteria
- Estimate complexity

#### Labels
- `bug`: Bug reports
- `feature`: New features
- `enhancement`: Improvements
- `documentation`: Documentation updates
- `security`: Security-related issues
- `performance`: Performance improvements

---

## 📞 Support and Contact

### Development Team
- **Lead Developer**: [Contact Information]
- **DevOps Engineer**: [Contact Information]
- **QA Engineer**: [Contact Information]

### Resources
- **Documentation**: This file and service-specific READMEs
- **Issue Tracker**: GitHub Issues
- **Discussions**: GitHub Discussions
- **Wiki**: GitHub Wiki

### Getting Help
1. Check existing documentation
2. Search GitHub Issues
3. Create new issue with appropriate template
4. Join team discussions

---

## 📄 License

This project is proprietary software. All rights reserved.

---

## 🔄 Changelog

### Version 1.0.0 (In Development)
- Initial microservices architecture
- Core service scaffolding
- Authentication system
- Basic API endpoints
- Development environment setup

---

*Last Updated: June 2, 2025*
*Documentation Version: 1.0.0*

