{"name": "@chakra-ui/utils", "version": "2.2.5", "description": "Common utilities and types for Chakra UI", "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "sideEffects": false, "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/legacy/utils"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "dependencies": {"@types/lodash.mergewith": "4.6.9", "lodash.mergewith": "4.6.2"}, "devDependencies": {"react": "^18.2.0"}, "peerDependencies": {"react": ">=16.8.0"}, "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json", "./*": {"import": {"types": "./dist/types/*.d.ts", "default": "./dist/esm/*.mjs"}, "require": {"types": "./dist/types/*.d.ts", "default": "./dist/cjs/*.cjs"}}}, "scripts": {"build": "tsup src --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src"}}