import type { TSESTree } from '@typescript-eslint/utils';
import type { SelectorsString } from './enums';
import type { Context, NormalizedSelector } from './types';
export declare function createValidator(type: SelectorsString, context: Context, allConfigs: NormalizedSelector[]): (node: TSESTree.Identifier | TSESTree.Literal | TSESTree.PrivateIdentifier) => void;
//# sourceMappingURL=validator.d.ts.map