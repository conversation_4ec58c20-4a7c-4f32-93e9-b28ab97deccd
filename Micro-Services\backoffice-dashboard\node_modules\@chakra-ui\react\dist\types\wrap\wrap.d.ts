import { SystemProps } from "@chakra-ui/styled-system";
import { HTMLChakraProps } from "../system";
export interface WrapProps extends HTMLChakraProps<"div"> {
    /**
     * The space between each child (even if it wraps)
     * @type SystemProps["margin"]
     */
    spacing?: SystemProps["margin"];
    /**
     * The horizontal space between the each child (even if it wraps). Defaults to `spacing` if not defined.
     * @type SystemProps["margin"]
     */
    spacingX?: SystemProps["margin"];
    /**
     * The vertical space between the each child (even if it wraps). Defaults to `spacing` if not defined.
     * @type SystemProps["margin"]
     */
    spacingY?: SystemProps["margin"];
    /**
     * The `justify-content` value (for cross-axis alignment)
     * @type SystemProps["justifyContent"]
     */
    justify?: SystemProps["justifyContent"];
    /**
     * The `align-items` value (for main axis alignment)
     * @type SystemProps["alignItems"]
     */
    align?: SystemProps["alignItems"];
    /**
     * The `flex-direction` value
     * @type SystemProps["flexDirection"]
     */
    direction?: SystemProps["flexDirection"];
    /**
     * If `true`, the children will be wrapped in a `WrapItem`
     * @default false
     */
    shouldWrapChildren?: boolean;
}
/**
 * Layout component used to stack elements that differ in length
 * and are liable to wrap.
 *
 * Common use cases:
 * - Buttons that appear together at the end of forms
 * - Lists of tags and chips
 *
 * @see Docs https://chakra-ui.com/wrap
 */
export declare const Wrap: import("../system").ComponentWithAs<"div", WrapProps>;
export interface WrapItemProps extends HTMLChakraProps<"li"> {
}
export declare const WrapItem: import("../system").ComponentWithAs<"li", WrapItemProps>;
