import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config()

const supabaseUrl = process.env.SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.')
}

// Service role client for admin operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Anonymous client for public operations
export const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: 'user' | 'admin' | 'support'
  language?: string
  timezone?: string
  preferences?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface AuthResponse {
  user: User | null
  session: any | null
  error: any | null
}

export interface UserProfile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: string
  language?: string
  timezone?: string
  preferences?: Record<string, any>
}

export default supabaseAdmin

