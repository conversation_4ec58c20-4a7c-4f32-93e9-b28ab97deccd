{"name": "framer-motion", "version": "10.18.0", "description": "A simple and powerful JavaScript animation library", "main": "dist/cjs/index.js", "module": "dist/es/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}, "./dom": {"types": "./dist/dom-entry.d.ts", "require": "./dist/cjs/dom-entry.js", "import": "./dist/es/dom-entry.mjs", "default": "./dist/cjs/dom-entry.js"}, "./package.json": "./package.json"}, "types": "dist/index.d.ts", "author": "Framer", "license": "MIT", "repository": "https://github.com/framer/motion/", "sideEffects": false, "keywords": ["react animation", "react", "three", "3d", "pose", "react pose", "animation", "gestures", "drag", "spring", "popmotion", "framer", "waapi"], "scripts": {"lint": "yarn eslint src/**/*.ts", "build": "yarn clean && tsc -p . && rollup -c && node ./scripts/check-bundle.js", "dev": "yarn watch", "clean": "rm -rf types dist lib", "test": "yarn test-server && yarn test-client", "test-ci": "yarn test", "test-client": "jest --config jest.config.json --max-workers=2", "test-server": "jest --config jest.config.ssr.json ", "test-watch": "jest --watch --coverage --coverageReporters=lcov --config jest.config.json", "test-appear": "yarn run collect-appear-tests && start-server-and-test 'pushd ../../; python -m SimpleHTTPServer; popd' http://0.0.0.0:8000 'cypress run -s cypress/integration/appear.chrome.ts --config baseUrl=http://localhost:8000/'", "test-projection": "yarn run collect-projection-tests && start-server-and-test 'pushd ../../; python -m SimpleHTTPServer; popd' http://0.0.0.0:8000 'cypress run -s cypress/integration/projection.chrome.ts --config baseUrl=http://localhost:8000/'", "test-e2e-chrome": "start-server-and-test start-dev-server http://localhost:9990 'cypress run --headless --browser chrome  --spec \"cypress/integration/layout-relative.chrome.ts\"'", "test-e2e-electron": "start-server-and-test start-dev-server http://localhost:9990 'cypress run --headless --config ignoreTestFiles=*.chrome.ts'", "test-e2e": "yarn test-appear && yarn test-projection && yarn test-e2e-electron", "test-e2e-file": "start-server-and-test start-dev-server http://localhost:9990 'cypress run --headless --spec \"cypress/integration/while-in-view.ts\"'", "collect-appear-tests": "node ../../dev/optimized-appear/collect-appear-tests.js", "collect-projection-tests": "node ../../dev/projection/collect-projection-tests.js", "prettier": "prettier ./src/* --write", "watch": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "prepack": "yarn build", "postpublish": "git push --tags", "measure": "rollup -c ./rollup.size.config.js && webpack --config webpack.size.config.js && bundlesize", "start-dev-server": "webpack serve --config ../../dev/webpack/config.js --hot"}, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}, "optionalDependencies": {"@emotion/is-prop-valid": "^0.8.2"}, "bundlesize": [{"path": "./dist/size-rollup-motion.js", "maxSize": "31.15 kB"}, {"path": "./dist/size-rollup-m.js", "maxSize": "5.33 kB"}, {"path": "./dist/size-rollup-dom-animation.js", "maxSize": "15.2 kB"}, {"path": "./dist/size-rollup-dom-max.js", "maxSize": "26.6 kB"}, {"path": "./dist/size-rollup-animate.js", "maxSize": "16.52 kB"}, {"path": "./dist/size-webpack-m.js", "maxSize": "5.45 kB"}, {"path": "./dist/size-webpack-dom-animation.js", "maxSize": "19.75 kB"}, {"path": "./dist/size-webpack-dom-max.js", "maxSize": "31.9 kB"}], "gitHead": "bf162648c3fd7635e324e65a5c22a6e2bcaac523"}